<template>
    <div class="index_header relative">
        <p>河南电力保供与电网风险推演平台</p>
        <div class="index_tap" v-if="state.type!='/'">
            <div>
                <div :class="['/index/FunctionModule1'].includes(state.type)?'have_select active':'have_select'" @click="changeUrl('/index/FunctionModule1')">电网实时运行展示</div>
                <div :class="['/index/FunctionModule2-1','/index/FunctionModule2-2','/index/FunctionModule2-2-1','/index/FunctionModule2-2-2','/index/FunctionModule2-3','/index/FunctionModule2-4',,'/index/FunctionModule2-4-1','/index/FunctionModule2-5'].includes(state.type)||state.typeShow2?'have_select active':'have_select'" @click="openList(2)">电网运行数据分析
                    <img src="@/assets/jt.png" alt="">
                </div>
                <div class="ul ul_sp2" v-if="state.typeShow2">
                    <p :class="state.type=='/index/FunctionModule2-1'?'active':''" @click.stop="changeUrl('/index/FunctionModule2-1')"><span></span>负荷特性分析</p>
                    <p @mouseenter="state.type2ChildShow2=true"  @mouseleave="state.type2ChildShow2=false" :class="['/index/FunctionModule2-2','/index/FunctionModule2-2-1','/index/FunctionModule2-2-2'].includes(state.type)?'active':''" @click.stop="changeUrl('/index/FunctionModule2-2')">
                        <span></span>电源特性分析
                        <div class="ul_small ul" v-if="state.type2ChildShow2">
                            <p :class="state.type=='/index/FunctionModule2-2-1'?'active':''" @click.stop="changeUrl('/index/FunctionModule2-2-1')"><span></span>常规机组特性分析</p>
                            <p :class="state.type=='/index/FunctionModule2-2-2'?'active':''" @click.stop="changeUrl('/index/FunctionModule2-2-2')"><span></span>统调新能源特性分析</p>
                        </div>
                    </p>
                    <p :class="state.type=='/index/FunctionModule2-3'?'active':''" @click.stop="changeUrl('/index/FunctionModule2-3')"><span></span>区外电力交换特性分析</p>
                    <p :class="state.type.includes('/index/FunctionModule2-4')?'active':''" @click.stop="changeUrl('/index/FunctionModule2-4')"><span></span>稳定断面特性分析</p>
                    <p :class="state.type=='/index/FunctionModule2-5'?'active':''" @click.stop="changeUrl('/index/FunctionModule2-5')"><span></span>典型时刻反演</p>
                </div>
            </div>
            <div class="index_title">
                <p>{{ getTitle() }}</p>
            </div>
            <div>
                <div :class="['/index/FunctionModule3-1','/index/FunctionModule3-2'].includes(state.type)||state.typeShow3?'have_select active':'have_select'" @click="openList(3)">规划态电网运行模拟
                    <img class="have_select_img" src="@/assets/jt.png" alt="">
                </div>
                <div class="ul ul_sp3" v-if="state.typeShow3">
                    <p :class="state.type=='/index/FunctionModule3-1'?'active':''" @click.stop="changeUrl('/index/FunctionModule3-1')"><span></span>全年运行态势模拟</p>
                    <p :class="state.type=='/index/FunctionModule3-2'?'active':''" @click.stop="changeUrl('/index/FunctionModule3-2')"><span></span>故障校验及断面推演</p>
                </div>
                <div :class="['/index/FunctionModule4-1','/index/FunctionModule4-2'].includes(state.type)||state.typeShow4?'have_select active':'have_select'" @click="openList(4)">中短期电网运行模拟
                    <img class="have_select_img" src="@/assets/jt.png" alt="">
                </div>
                <div class="ul ul_sp4" v-if="state.typeShow4">
                    <p :class="state.type=='/index/FunctionModule4-1'?'active':''" @click.stop="changeUrl('/index/FunctionModule4-1')"><span></span>3~7天运行态势模拟</p>
                    <p :class="state.type=='/index/FunctionModule4-2'?'active':''" @click.stop="changeUrl('/index/FunctionModule4-2')"><span></span>故障校验及断面推演</p>
                </div>
                <!-- <div :class="['/FunctionModule4'].includes(state.type)?'have_select active':'have_select'" @click.stop="changeUrl('/FunctionModule4')">中短期电网运行模拟</div> -->
            </div>
        </div>
        <img v-if="state.type&&state.type!='/'" @click="backHome" class="icon pointer" src="@/assets/images/index/icon_home.png" alt="">
    </div>
</template>
<script setup>
import { dataStore } from '@/store/dataStore'
import { storeToRefs } from 'pinia'
import { watch ,computed,onMounted} from 'vue';
import { useRouter } from 'vue-router'
import {  reactive, ref } from '@vue/reactivity'
const router = useRouter()
const store = dataStore()
const { year1,year2,token,end_time } = storeToRefs(store)
const state = reactive({
    type: undefined,
    typeShow2:false,
    typeShow3:false,
    typeShow4:false,
    type2ChildShow2:false,
})
const timer = ref()
const openList = (index) => {
    if (index == 2) {
        state.typeShow3 = false
        state.typeShow4 = false
    }else if (index == 3) {
        state.typeShow2 = false
        state.typeShow4 = false
    } else if (index == 4) {
        state.typeShow2 = false
        state.typeShow3 = false
    }
    state[`typeShow${index}`] = !state[`typeShow${index}`] 
    // closeAll()
}
const changeUrl = (url) => {
    closeAll()
    router.push({
        path:url,
    })
}
const backHome = () => {
    if(location.origin=='http://localhost:7777') return
    window.location.href = location.origin+'/admin'
}   
const closeAll = () => {
    state.typeShow2 = false
    state.typeShow3 = false
    state.typeShow4 = false
    state.type2ChildShow2 = false
}
const getTitle = computed(() => () => {
    if (router.currentRoute.value.fullPath == "/index/FunctionModule1") {
        return ''
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule3-1") { 
        return year1.value +'年时序运行模拟'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-1"){
        return '负荷特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-2"){
        return '电源特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-2-1"){
        return '常规电源特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-2-2"){
        return '新能源特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-3"){
        return '区外电力交换特性分析'
    } else if (router.currentRoute.value.fullPath.includes("/index/FunctionModule2-4")){
        return '稳定断面特性分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule2-5"){
        return '典型时刻潮流分析'
    } else if (router.currentRoute.value.fullPath == "/index/FunctionModule4-1") { 
        return year2.value +'年时序运行模拟——短期模拟'
    } else {
        return ''
    }
})
onMounted(()=>{
    document.addEventListener('click', (e) => {
        if (e.target.className.length&&(e.target.className.includes('have_select')||e.target.className.includes('have_select_img'))) {
        
        } else {
            closeAll()
        }
    })
})
watch(() => router.currentRoute.value.path, (toPath) => {
    state.type = toPath
},{immediate: true,deep: true})
watch(() => token.value,(v) => {
    if (!v) {
        changeUrl('/')
    } else {
        const timeout = new Date(end_time.value).getTime() - Date.now()
        timer.value = setTimeout(() => {
            token.value = undefined
            sessionStorage.removeItem('token')
            sessionStorage.removeItem('end_time')
            changeUrl('/')
        }, timeout);
    }
},{immediate: true,deep: true})
</script>
<style lang="scss" scoped>
    .index_header{
        height: 189px!important;
        >img{
            position: absolute;
            right: 0px;
            top: 25px;
            width: 49px;
            height: 42px;
        }
        >p:first-child{
            font-size: 46px;
            line-height: 100px;
            height: 126px;
            font-family: nanshen, sans-serif;
            letter-spacing: 5px;
            color: #d7fffe;
            text-align: center;
        }
        .index_tap{
            display: flex;
            justify-content: space-between;
            align-items: center;
            // justify-content: center;
            height: calc(189px - 126px);
            >div:first-child,>div:last-child{
                display: flex;
                position: relative;
                >.have_select{
                    color: rgb(48, 215, 227);
                    position: relative;
                    font-size: 16px;
                    width: 270px;
                    text-align: center;
                    height: 53px;
                    line-height: 53px;
                    background-image: url('@/assets/index_bg.png');
                    background-size: 100% 100%;
                    &:hover{
                        cursor: pointer;
                        background-image: url('@/assets/index_bgs.png');
                        color:  rgb(154, 248, 255);
                    }
                    img{
                        width: 18px;
                        height: 9px;
                        position: absolute; 
                        bottom: -6px;
                        left: 50%;
                    }
                }
                >div.active{
                    background-image: url('@/assets/index_bgs.png');
                    color:  rgb(154, 248, 255);
                }
                .have_select_disabled{
                    &:hover{
                        cursor: not-allowed;
                        background-image: url('@/assets/index_bg.png');
                        color: rgb(48, 215, 227);
                    }
                }
                >div:first-child{
                    margin-right: 10px;
                }
                .ul{
                    position: absolute;
                    z-index: 9;
                    width: 270px;
                    left: 280px;
                    top:60px;
                    border: 1px solid rgb(7, 152, 161);
                    border-radius: 4px;
                    background: rgb(4, 79, 82);
                    p{
                        color: rgb(145, 232, 239);
                        font-size: 20px;
                        line-height: 28px;
                        padding:10px 20px;
                        display: flex;
                        align-items: center;
                        span{
                            width: 6px;
                            height: 6px;
                            border-radius: 100%;
                            background: rgb(75, 199, 216);
                            margin-right: 10px;
                        }
                        &:hover{
                            cursor: pointer;
                            color: #fff;
                            font-weight: bolder;
                            span{
                                background: #fff;
                            }
                        }
                    }
                    p.active{
                        color: #fff;
                        font-weight: bolder;
                        span{
                            background: #fff;
                        }
                    }
                }
                .ul_small{
                    position: absolute;
                    left: 100%;
                    top: 50px;
                    p{
                        padding: 5px 10px;
                    }
                }
                .ul_sp3{
                    left: 0px;
                }
                .ul_sp4{
                    right: 0;
                }
            }
        }
        .index_select{
            position: absolute;
            right: 580px;
            top: 140px;
            &:deep(.ant-select){
                width: 120px;
            }
        }
        .index_title{
            >p{
                color: $activeTextColor;
                font-size: 38px;
                font-weight: bolder;
                letter-spacing: 0px;
            }
        }
    }
</style>