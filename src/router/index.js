import { createWebHashHistory, createRouter } from 'vue-router';
const routes = [
    {
        path: '/home',
        name: 'home',
        component: () => import(/* webpackChunkName: "HomePage" */ '../views/HomePage/IndexView.vue')
    },
    {
        path: '/',
        name: 'login',
        component: () => import(/* webpackChunkName: "HomePage" */ '../views/HomePage/LoginView.vue')
    },
    {
        path: '/index',
        name: 'index',
        component: () => import(/* webpackChunkName: "Index" */ '../views/IndexRouter/IndexView.vue'),
        children: [
            {
                path: 'FunctionModule1',
                name: 'FunctionModule1',
                component: () => import(/* webpackChunkName: "IndexMain" */ '../views/FunctionModule1/IndexView.vue'),
            },
            {
                path: 'FunctionModule2-1',
                name: 'FunctionModule2-1',
                component: () => import(/* webpackChunkName: "FunctionModule2-1" */ '../views/FunctionModule2/IndexView1.vue'),
            },
             {
                path: 'FunctionModule2-2',
                name: 'FunctionModule2-2',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView2.vue'),
            },
            {
                path: 'FunctionModule2-2-1',
                name: 'FunctionModule2-2-1',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView2_1.vue'),
            },
            {
                path: 'FunctionModule2-2-2',
                name: 'FunctionModule2-2-2',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView2_2.vue'),
            },
            {
                path: 'FunctionModule2-3',
                name: 'FunctionModule2-3',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView3.vue'),
            },
            {
                path: 'FunctionModule2-4',
                name: 'FunctionModule2-4',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView4.vue'),
            },
            {
                path: 'FunctionModule2-4-1',
                name: 'FunctionModule2-4-1',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView4_1.vue'),
            },
            {
                path: 'FunctionModule2-5',
                name: 'FunctionModule2-5',
                component: () => import(/* webpackChunkName: "FunctionModule2-2" */ '../views/FunctionModule2/IndexView5.vue'),
            },
            {
                path: 'FunctionModule3-1',
                name: 'FunctionModule3-1',
                component: () => import(/* webpackChunkName: "FunctionModule3-1" */ '../views/FunctionModule3/IndexView1.vue'),
            },
            {
                path: 'FunctionModule3-2',
                name: 'FunctionModule3-2',
                component: () => import(/* webpackChunkName: "FunctionModule3-2" */ '../views/FunctionModule3/IndexView2.vue'),
            },
            {
                path: 'FunctionModule4-1',
                name: 'FunctionModule4-1',
                component: () => import(/* webpackChunkName: "FunctionModule4" */ '../views/FunctionModule4/IndexView1.vue'),
            },
            {
                path: 'FunctionModule4-2',
                name: 'FunctionModule4-2',
                component: () => import(/* webpackChunkName: "FunctionModule4" */ '../views/FunctionModule4/IndexView2.vue'),
            },
        ]
    },
    {
        path:'/:xxxx',
        redirect:{
            name: 'home',
        }
    }
];
const router = createRouter({
    history: createWebHashHistory(), 
    routes, 
});
// router.beforeEach((to, from, next) => {
//     if(to.path='/'){
//         next()
//     }
// })
export default router;
