# UI重构系统设计文档

## 1. 整体架构设计

### 1.1 架构概览

```mermaid
graph TB
    subgraph "视图层 (View Layer)"
        V1[LoginView]
        V2[IndexView]
        V3[FunctionModule1]
        V4[FunctionModule2]
        V5[FunctionModule3]
        V6[FunctionModule4]
    end
    
    subgraph "业务组件层 (Business Components)"
        BC1[数据表格组件]
        BC2[图表容器组件]
        BC3[指标卡片组件]
        BC4[导航组件]
    end
    
    subgraph "UI组件库 (UI Components)"
        UI1[DarkTable]
        UI2[ChartFrame]
        UI3[IndicatorBox]
        UI4[TitleBox]
        UI5[TabComponent]
    end
    
    subgraph "样式系统 (Style System)"
        S1[主题变量]
        S2[暗色主题]
        S3[组件样式]
        S4[布局系统]
    end
    
    subgraph "数据层 (Data Layer)"
        D1[Pinia Store]
        D2[API Services]
        D3[Utils]
    end
    
    V1 --> BC1
    V2 --> BC2
    V3 --> BC3
    V4 --> BC4
    
    BC1 --> UI1
    BC2 --> UI2
    BC3 --> UI3
    BC4 --> UI4
    
    UI1 --> S1
    UI2 --> S2
    UI3 --> S3
    UI4 --> S4
    
    V1 --> D1
    V2 --> D2
    V3 --> D3
```

### 1.2 分层设计原则

1. **视图层 (View Layer)**：负责页面布局和用户交互
2. **业务组件层 (Business Components)**：封装业务逻辑和数据处理
3. **UI组件库 (UI Components)**：提供可复用的UI元素
4. **样式系统 (Style System)**：统一的主题和样式管理
5. **数据层 (Data Layer)**：数据管理和API调用（保持不变）

## 2. 核心组件设计

### 2.1 UI组件库结构

```
src/components/ui/
├── DarkTable/           # 暗色主题表格
│   ├── index.vue
│   └── style.scss
├── ChartFrame/          # 图表容器框架
│   ├── index.vue
│   └── style.scss
├── IndicatorBox/        # 指标展示框
│   ├── index.vue
│   └── style.scss
├── TitleBox/            # 标题框组件
│   ├── index.vue
│   └── style.scss
├── TabComponent/        # 选项卡组件
│   ├── index.vue
│   └── style.scss
└── index.js            # 组件导出
```

### 2.2 组件接口规范

#### DarkTable 组件
```typescript
interface DarkTableProps {
  columns: Array<{
    title: string;
    dataIndex: string;
    key: string;
    width?: number;
  }>;
  dataSource: Array<Record<string, any>>;
  loading?: boolean;
  pagination?: boolean | object;
  size?: 'small' | 'middle' | 'large';
}

interface DarkTableEmits {
  change: (pagination: any, filters: any, sorter: any) => void;
  row-click: (record: any, index: number) => void;
}
```

#### ChartFrame 组件
```typescript
interface ChartFrameProps {
  title?: string;
  height?: number | string;
  loading?: boolean;
  theme?: 'dark' | 'light';
  chartOptions?: EChartsOption;
}

interface ChartFrameEmits {
  chart-ready: (chartInstance: ECharts) => void;
  chart-click: (params: any) => void;
}
```

#### IndicatorBox 组件
```typescript
interface IndicatorBoxProps {
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string | number;
  icon?: string;
}
```

## 3. 样式系统设计

### 3.1 主题变量系统

```scss
// src/styles/themes/dark.scss
:root {
  // 主色调
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  
  // 背景色
  --bg-color-primary: #0a0e1a;
  --bg-color-secondary: #1a1f2e;
  --bg-color-tertiary: #2a2f3e;
  
  // 文字颜色
  --text-color-primary: #ffffff;
  --text-color-secondary: #b8c5d1;
  --text-color-tertiary: #8a9ba8;
  
  // 边框颜色
  --border-color-primary: #3a3f4e;
  --border-color-secondary: #4a4f5e;
  
  // 图表颜色
  --chart-color-1: #1890ff;
  --chart-color-2: #52c41a;
  --chart-color-3: #faad14;
  --chart-color-4: #f5222d;
  --chart-color-5: #722ed1;
}
```

### 3.2 组件样式规范

```scss
// src/styles/mixins/ui-components.scss
@mixin chart-frame {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color-primary);
  border-radius: 8px;
  padding: 16px;
  
  .chart-title {
    color: var(--text-color-primary);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
  }
}

@mixin indicator-box {
  background: linear-gradient(135deg, var(--bg-color-secondary) 0%, var(--bg-color-tertiary) 100%);
  border: 1px solid var(--border-color-primary);
  border-radius: 6px;
  padding: 12px 16px;
  
  .indicator-value {
    color: var(--primary-color);
    font-size: 24px;
    font-weight: 600;
  }
  
  .indicator-label {
    color: var(--text-color-secondary);
    font-size: 12px;
  }
}
```

## 4. 模块重构映射

### 4.1 功能模块对应关系

| 现有模块 | 对应UI设计 | 主要组件 | 重构重点 |
|---------|-----------|----------|----------|
| FunctionModule1 | 界面1 | 电力平衡分析、新能源出力、旋转备用 | 三面板布局、ECharts暗色主题 |
| FunctionModule2 | 界面2 | 优化分析、六维图表 | 右侧布局、雷达图组件 |
| FunctionModule3 | 界面3 | 参数配置、结果展示 | 参数面板、结果表格 |
| FunctionModule4 | 界面2/3 | 综合分析 | 复合布局、多图表联动 |

### 4.2 组件复用策略

```mermaid
graph LR
    subgraph "共享组件"
        A[ChartFrame]
        B[DarkTable]
        C[IndicatorBox]
        D[TitleBox]
    end
    
    subgraph "FunctionModule1"
        E[电力平衡图表]
        F[新能源出力图表]
        G[旋转备用图表]
    end
    
    subgraph "FunctionModule2"
        H[优化结果表格]
        I[六维雷达图]
    end
    
    subgraph "FunctionModule3"
        J[参数配置表格]
        K[结果展示图表]
    end
    
    A --> E
    A --> F
    A --> G
    A --> I
    A --> K
    
    B --> H
    B --> J
    
    C --> E
    C --> F
    C --> G
    
    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    D --> J
    D --> K
```

## 5. 数据流向设计

### 5.1 数据流架构

```mermaid
sequenceDiagram
    participant U as User
    participant V as View Component
    participant BC as Business Component
    participant UI as UI Component
    participant S as Pinia Store
    participant API as API Service
    
    U->>V: 用户交互
    V->>BC: 触发业务逻辑
    BC->>S: 获取/更新状态
    S->>API: API调用
    API-->>S: 返回数据
    S-->>BC: 状态更新
    BC->>UI: 传递数据
    UI-->>V: 渲染更新
    V-->>U: 界面反馈
```

### 5.2 状态管理扩展

```typescript
// src/store/uiStore.js
import { defineStore } from 'pinia'

export const useUIStore = defineStore('ui', {
  state: () => ({
    theme: 'dark', // 主题模式
    sidebarCollapsed: false, // 侧边栏状态
    chartTheme: 'dark', // 图表主题
    tableSize: 'middle', // 表格尺寸
  }),
  
  actions: {
    toggleTheme() {
      this.theme = this.theme === 'dark' ? 'light' : 'dark'
      this.chartTheme = this.theme
    },
    
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },
    
    setTableSize(size: 'small' | 'middle' | 'large') {
      this.tableSize = size
    }
  }
})
```

## 6. ECharts主题配置

### 6.1 暗色主题定义

```javascript
// src/utils/chartTheme.js
export const darkTheme = {
  color: [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', 
    '#722ed1', '#fa8c16', '#a0d911', '#13c2c2'
  ],
  
  backgroundColor: 'transparent',
  
  textStyle: {
    color: '#ffffff',
    fontFamily: 'PingFang SC, Microsoft YaHei, sans-serif'
  },
  
  title: {
    textStyle: {
      color: '#ffffff',
      fontSize: 16,
      fontWeight: 500
    }
  },
  
  legend: {
    textStyle: {
      color: '#b8c5d1'
    }
  },
  
  grid: {
    borderColor: '#3a3f4e',
    backgroundColor: 'transparent'
  },
  
  categoryAxis: {
    axisLine: {
      lineStyle: {
        color: '#3a3f4e'
      }
    },
    axisTick: {
      lineStyle: {
        color: '#3a3f4e'
      }
    },
    axisLabel: {
      color: '#8a9ba8'
    },
    splitLine: {
      lineStyle: {
        color: '#2a2f3e'
      }
    }
  },
  
  valueAxis: {
    axisLine: {
      lineStyle: {
        color: '#3a3f4e'
      }
    },
    axisTick: {
      lineStyle: {
        color: '#3a3f4e'
      }
    },
    axisLabel: {
      color: '#8a9ba8'
    },
    splitLine: {
      lineStyle: {
        color: '#2a2f3e'
      }
    }
  }
}
```

## 7. 异常处理策略

### 7.1 错误边界组件

```vue
<!-- src/components/ErrorBoundary.vue -->
<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <h3>组件渲染异常</h3>
      <p>{{ errorMessage }}</p>
      <a-button @click="retry">重试</a-button>
    </div>
  </div>
  <slot v-else />
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'

const hasError = ref(false)
const errorMessage = ref('')

onErrorCaptured((error) => {
  hasError.value = true
  errorMessage.value = error.message
  console.error('Component Error:', error)
  return false
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
}
</script>
```

### 7.2 图表降级策略

```javascript
// src/utils/chartFallback.js
export const createChartWithFallback = (container, options) => {
  try {
    const chart = echarts.init(container, 'dark')
    chart.setOption(options)
    return chart
  } catch (error) {
    console.error('Chart initialization failed:', error)
    // 显示静态图片或简化版本
    container.innerHTML = `
      <div class="chart-fallback">
        <p>图表加载失败，请刷新重试</p>
        <button onclick="location.reload()">刷新页面</button>
      </div>
    `
  }
}
```

## 8. 性能优化方案

### 8.1 组件懒加载

```javascript
// src/router/index.js 中的优化
const routes = [
  {
    path: '/function1',
    name: 'FunctionModule1',
    component: () => import(
      /* webpackChunkName: "function1" */ 
      '@/views/FunctionModule1/IndexView.vue'
    )
  }
]
```

### 8.2 图表实例管理

```javascript
// src/composables/useChart.js
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export function useChart(containerRef) {
  const chartInstance = ref(null)
  
  onMounted(() => {
    if (containerRef.value) {
      chartInstance.value = echarts.init(containerRef.value, 'dark')
    }
  })
  
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
  })
  
  return {
    chartInstance,
    setOption: (options) => {
      chartInstance.value?.setOption(options)
    },
    resize: () => {
      chartInstance.value?.resize()
    }
  }
}
```

## 9. 兼容性和迁移策略

### 9.1 渐进式升级

1. **阶段1**：创建UI组件库和样式系统
2. **阶段2**：重构FunctionModule1（作为试点）
3. **阶段3**：依次重构其他功能模块
4. **阶段4**：优化和性能调优

### 9.2 向后兼容

- 保持现有API接口不变
- 保持现有路由结构
- 保持现有状态管理逻辑
- 提供主题切换功能（新旧UI并存）

## 10. 质量保证

### 10.1 测试策略

- **单元测试**：UI组件的props、events、slots测试
- **集成测试**：业务组件与数据层的集成测试
- **E2E测试**：关键用户流程的端到端测试
- **视觉回归测试**：UI变更的视觉对比测试

### 10.2 代码质量

- 使用ESLint + Prettier进行代码规范
- 使用TypeScript增强类型安全
- 组件文档和使用示例
- 性能监控和优化建议

---

**设计文档版本**: v1.0  
**创建日期**: 2024年  
**负责人**: AI Assistant  
**审核状态**: 待审核