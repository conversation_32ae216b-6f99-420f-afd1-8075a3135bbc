# Vue3 Admin Dashboard (existing project analysis and plan)

## Core Features

- Dashboard with KPIs and responsive charts

- Data list with advanced filters and pagination

- Detail/editor forms with validation

- Role-based navigation and guarded routes

- Notifications and empty/error states

- Theming (light/dark) and density controls

- Responsive, mobile-friendly layout

## Tech Stack

{
  "Web": {
    "arch": "vue",
    "component": null,
    "ui_library": "ant-design-vue",
    "state": "pinia",
    "router": "vue-router",
    "charts": "echarts",
    "utils": "dayjs, amfe-flexible",
    "styles": "scss",
    "structure": [
      "api",
      "request",
      "components",
      "views",
      "utils",
      "styles",
      "assets",
      "config",
      "store"
    ],
    "entry": "main.js -> App.vue -> router"
  }
}

## Design

Glassmorphism-inspired, enterprise-clean UI with translucent cards, blue/cyan accents, 12-col responsive grid, smooth micro-interactions, sticky top nav and compact bottom nav; four core pages: Dashboard, Data List, Detail/Editor, Settings.

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[ ] Review router and views; enable route-level code-splitting and define a base layout route.

[ ] Create BaseLayout with Header, Sidebar (optional), and Footer/BottomNav; wire into router.

[ ] Implement Breadcrumbs and active route highlighting in Header.

[ ] Set up Pinia stores: user (auth/roles), app (theme/density), and data (list/detail cache).

[ ] Add selective persistence for user/app stores (e.g., localStorage).

[ ] Implement request client with base URL, interceptors (token, error normalization), and retry toggle.

[ ] Build api modules for entities (list, detail, create, update, delete) using request client.

[ ] Configure global day/time helpers and filters; centralize formats.

[ ] Create reusable UI components: PageHeader, CardSection, EmptyState, LoadingSkeleton.

[ ] Implement Dashboard view: KPI cards with delta, ECharts line/bar/pie components, responsive resize.

[ ] Implement Data List: filter form (collapsible), table with sorting/selection, pagination, batch actions.

[ ] Implement Detail/Editor: form sections with validation, submit handlers, optimistic updates or reload.

[ ] Add route guards (beforeEach): auth check, role-based access, redirect on failure.

[ ] Integrate global notifications and modal confirm patterns for destructive actions.

[ ] Set up theme tokens and SCSS variables; implement light/dark toggle and density switch.

[ ] Apply responsive grid and typography; verify amfe-flexible rem scaling across breakpoints.

[ ] Add chart/theme synchronization and window resize observers; debounce reflow.

[ ] Create utils: formatNumber, formatDate, debounce, throttle, safeJSON.

[ ] Implement error boundaries at view level; fallback EmptyState with retry.

[ ] Optimize imports and chunking; prefetch Dashboard and Data List assets.
